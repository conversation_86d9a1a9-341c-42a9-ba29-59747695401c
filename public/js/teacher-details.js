/**
 * Teacher Details JavaScript
 * Handles teacher details page functionality including modal, search, and filters
 */

console.log('🚀 Teacher Details JavaScript loaded!');

$(document).ready(function() {
    console.log('✅ Teacher Details System Initialized!');

    // Global variables
    let currentTeacherData = null;
    let currentTeacherId = null;

    // Initialize functionality
    initializeEventHandlers();
    initializeSearch();
    initializeFilters();
    initializeCheckboxes();

    /**
     * Initialize all event handlers
     */
    function initializeEventHandlers() {
        // View teacher button
        $(document).on('click', '.viewTeacherBtn', handleViewTeacher);
        
        // Edit teacher button
        $(document).on('click', '.editTeacherBtn', handleEditTeacher);
        
        // Generate CV button
        $(document).on('click', '.generateCVBtn', handleGenerateCV);
        
        // Modal close buttons
        $(document).on('click', '#closeModalBtn', closeModal);
        
        // Download CV from modal
        $(document).on('click', '#downloadCVBtn', handleDownloadCV);
        
        // Tab navigation
        $(document).on('click', '.tab-btn', handleTabSwitch);
        
        // Refresh data button
        $(document).on('click', '#refreshData', handleRefreshData);
        
        // Export data button
        $(document).on('click', '#exportData', handleExportData);

        // Export selected button
        $(document).on('click', '#exportSelectedBtn', handleExportSelected);

        // Clear selection button
        $(document).on('click', '#clearSelectionBtn', handleClearSelection);

        // Modal background click to close
        $(document).on('click', '#teacherModal', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Escape key to close modal
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && !$('#teacherModal').hasClass('hidden')) {
                closeModal();
            }
        });
    }

    /**
     * Initialize search functionality
     */
    function initializeSearch() {
        $('#searchInput').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            filterTeachers();
        });
    }

    /**
     * Initialize filter functionality
     */
    function initializeFilters() {
        $('#departmentFilter, #statusFilter').on('change', function() {
            filterTeachers();
        });
    }

    /**
     * Initialize checkbox functionality
     */
    function initializeCheckboxes() {
        // Select all checkbox
        $(document).on('change', '#selectAllTeachers', function() {
            const isChecked = $(this).prop('checked');
            $('.teacher-checkbox:visible').prop('checked', isChecked);
            updateSelectionUI();
        });

        // Individual teacher checkboxes
        $(document).on('change', '.teacher-checkbox', function() {
            updateSelectAllState();
            updateSelectionUI();
        });
    }

    /**
     * Filter teachers based on search and filters
     */
    function filterTeachers() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const departmentFilter = $('#departmentFilter').val();
        const statusFilter = $('#statusFilter').val();

        $('.teacher-row').each(function() {
            const $row = $(this);
            const name = $row.data('name') || '';
            const email = $row.data('email') || '';
            const department = $row.data('department') || '';
            const status = $row.data('status') || '';
            
            let showRow = true;
            
            // Search filter
            if (searchTerm && !name.includes(searchTerm) && !email.includes(searchTerm)) {
                showRow = false;
            }
            
            // Department filter
            if (departmentFilter && department !== departmentFilter) {
                showRow = false;
            }
            
            // Status filter
            if (statusFilter && status !== statusFilter) {
                showRow = false;
            }
            
            $row.toggle(showRow);
        });

        // Update selection UI after filtering
        updateSelectionUI();
    }

    /**
     * Update the select all checkbox state
     */
    function updateSelectAllState() {
        const $visibleCheckboxes = $('.teacher-checkbox:visible');
        const $checkedCheckboxes = $('.teacher-checkbox:visible:checked');
        const $selectAll = $('#selectAllTeachers');

        if ($visibleCheckboxes.length === 0) {
            $selectAll.prop('indeterminate', false).prop('checked', false);
        } else if ($checkedCheckboxes.length === $visibleCheckboxes.length) {
            $selectAll.prop('indeterminate', false).prop('checked', true);
        } else if ($checkedCheckboxes.length > 0) {
            $selectAll.prop('indeterminate', true).prop('checked', false);
        } else {
            $selectAll.prop('indeterminate', false).prop('checked', false);
        }
    }

    /**
     * Update the selection UI (count, buttons)
     */
    function updateSelectionUI() {
        const selectedCount = $('.teacher-checkbox:checked').length;

        $('#selectedCount').text(selectedCount);

        if (selectedCount > 0) {
            $('#selectedTeachersInfo').removeClass('hidden');
            $('#exportSelectedBtn').removeClass('hidden');
            $('#clearSelectionBtn').removeClass('hidden');
        } else {
            $('#selectedTeachersInfo').addClass('hidden');
            $('#exportSelectedBtn').addClass('hidden');
            $('#clearSelectionBtn').addClass('hidden');
        }

        updateSelectAllState();
    }

    /**
     * Handle view teacher button click
     */
    function handleViewTeacher(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        
        if (!teacherId) {
            console.error('No teacher ID found');
            return;
        }
        
        console.log('Opening modal for teacher ID:', teacherId);
        currentTeacherId = teacherId;
        openModal(teacherId);
    }

    /**
     * Handle edit teacher button click
     */
    function handleEditTeacher(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        
        // Placeholder for edit functionality
        ToastNotifications.info(`Edit functionality for teacher ID: ${teacherId} will be implemented`);
    }

    /**
     * Handle generate CV button click
     */
    function handleGenerateCV(e) {
        e.preventDefault();
        const $button = $(this);
        const teacherId = $button.data('teacher-id');
        const teacherName = $button.data('teacher-name');
        
        generateTeacherCV(teacherId, teacherName, $button);
    }

    /**
     * Handle download CV from modal
     */
    function handleDownloadCV(e) {
        e.preventDefault();
        
        if (currentTeacherData) {
            generateTeacherCV(
                currentTeacherData.id,
                currentTeacherData.name,
                $(this)
            );
        }
    }

    /**
     * Handle refresh data button click
     */
    function handleRefreshData(e) {
        e.preventDefault();
        const $button = $(this);
        const originalHtml = $button.html();
        
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...');
        
        setTimeout(() => {
            location.reload();
        }, 500);
    }

    /**
     * Handle export data button click
     */
    function handleExportData(e) {
        e.preventDefault();

        console.log('🔄 Opening teacher export modal...');
        openExportModal();
    }

    /**
     * Handle export selected button click
     */
    function handleExportSelected(e) {
        e.preventDefault();

        const selectedTeachers = getSelectedTeachers();
        if (selectedTeachers.length === 0) {
            ToastNotifications.warning('Please select at least one teacher to export.');
            return;
        }

        console.log('🔄 Opening teacher export modal for selected teachers...');
        openExportModal();
    }

    /**
     * Handle clear selection button click
     */
    function handleClearSelection(e) {
        e.preventDefault();

        $('.teacher-checkbox').prop('checked', false);
        $('#selectAllTeachers').prop('checked', false);
        updateSelectionUI();
    }

    /**
     * Get selected teachers
     */
    function getSelectedTeachers() {
        const selectedTeachers = [];
        $('.teacher-checkbox:checked').each(function() {
            selectedTeachers.push({
                id: $(this).val(),
                name: $(this).data('teacher-name')
            });
        });
        return selectedTeachers;
    }

    /**
     * Handle tab switching in modal
     */
    function handleTabSwitch(e) {
        e.preventDefault();
        const $button = $(this);
        const tabName = $button.data('tab');
        
        // Update tab buttons
        $('.tab-btn').removeClass('active border-gray-600 text-gray-900')
                     .addClass('border-transparent text-gray-500');
        
        $button.removeClass('border-transparent text-gray-500')
               .addClass('active border-gray-600 text-gray-900');
        
        // Update tab content
        $('.tab-content').addClass('hidden');
        $(`#${tabName}Tab`).removeClass('hidden');
    }

    /**
     * Open teacher modal
     */
    function openModal(teacherId) {
        $('#teacherModal').removeClass('hidden');
        $('#modalLoading').show();
        $('.tab-content').addClass('hidden');
        $('#personalTab').removeClass('hidden');
        
        // Reset tab buttons
        $('.tab-btn').removeClass('active border-gray-600 text-gray-900')
                     .addClass('border-transparent text-gray-500');
        $('.tab-btn[data-tab="personal"]').removeClass('border-transparent text-gray-500')
                                          .addClass('active border-gray-600 text-gray-900');
        
        // Load teacher data
        loadTeacherData(teacherId);
    }

    /**
     * Close modal
     */
    function closeModal() {
        $('#teacherModal').addClass('hidden');
        currentTeacherData = null;
        currentTeacherId = null;
    }

    /**
     * Load teacher data for modal
     */
    function loadTeacherData(teacherId) {
        // Show loading state
        $('#modalLoading').show();
        
        console.log('Loading teacher data for ID:', teacherId);
        
        // Fetch teacher data from API
        $.ajax({
            url: `/principal/api/teacher/profile-enhanced`,
            method: 'GET',
            data: { teacher_id: teacherId },
            success: function(response) {
                console.log('API Response:', response);
                if (response.success && response.teacher) {
                    currentTeacherData = response.teacher;
                    populateModal(response.teacher);
                } else {
                    console.error('Failed to load teacher data:', response.message);
                    showModalError('Failed to load teacher data: ' + (response.message || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading teacher data:', error);
                console.error('XHR Status:', xhr.status);
                console.error('Response Text:', xhr.responseText);
                showModalError('Error loading teacher data: ' + error);
            },
            complete: function() {
                $('#modalLoading').hide();
            }
        });
    }

    /**
     * Populate modal with teacher data
     */
    function populateModal(data) {
        console.log('Populating modal with data:', data);
        
        // Update modal title and general info
        const teacherName = data.displayName || data.name || data.full_name || 'Teacher';
        const designation = data.designation || 'Teacher';
        const department = data.department || 'Academic Department';
        
        $('#modalTitle').text(`${teacherName} - Details`);
        $('#modalSubtitle').text(`${designation} • ${department}`);
        
        // Update teacher general info section
        const initials = teacherName.split(' ').map(n => n[0]).join('').toUpperCase();
        $('#teacherInitials').text(initials);
        $('#teacherName').text(teacherName);
        $('#teacherDesignation').text(`${designation} • ${department}`);
        $('#teacherContact').text(`${data.email || 'No email'} • ${data.phone || 'No phone'}`);
        $('#employeeId').text(data.employee_id || 'No ID');
        $('#experienceInfo').text(`${data.total_experience_years || 0} years experience`);
        
        // Update status badge
        if (data.is_active) {
            $('#statusBadge').removeClass('bg-red-100 text-red-800').addClass('bg-green-100 text-green-800').text('Active');
        } else {
            $('#statusBadge').removeClass('bg-green-100 text-green-800').addClass('bg-red-100 text-red-800').text('Inactive');
        }
        
        // Populate personal information tab
        populatePersonalTab(data);
        
        // Populate other tabs with enhanced data
        populateEducationTab(data);
        populateExperienceTab(data);
        populateSkillsTab(data);
        populateAchievementsTab(data);
    }

    /**
     * Populate personal information tab
     */
    function populatePersonalTab(data) {
        $('#modalDateOfBirth').text(data.date_of_birth || '-');
        $('#modalGender').text(data.gender || '-');
        $('#modalUsername').text(data.username || '-');
        $('#modalJoiningDate').text(data.joining_date || '-');
        $('#modalEmergencyContact').text(data.emergency_contact || '-');
        $('#modalAddress').text(data.address || '-');
        $('#modalOfficeLocation').text(data.office_location || '-');
        $('#modalLanguages').text(data.languages_known || '-');
    }

    /**
     * Populate education tab
     */
    function populateEducationTab(data) {
        const $content = $('#modalEducationContent');

        // Check for enhanced education timeline data first
        if (data.educationTimeline && data.educationTimeline.length > 0) {
            let html = '<div class="space-y-4">';
            data.educationTimeline.forEach(edu => {
                html += `
                    <div class="border-l-4 border-gray-300 pl-4">
                        <h5 class="font-semibold text-gray-900">${edu.title || 'Qualification'}</h5>
                        <p class="text-sm text-gray-600">${edu.institution || 'Institution'}</p>
                        <div class="text-xs text-gray-500 mt-1">
                            <span>${edu.year || 'Year'}</span>
                            ${edu.percentage ? ` • ${edu.percentage}%` : ''}
                            ${edu.grade ? ` • Grade: ${edu.grade}` : ''}
                            ${edu.cgpa ? ` • CGPA: ${edu.cgpa}` : ''}
                        </div>
                        ${edu.specialization ? `<p class="text-xs text-gray-600 mt-1">Specialization: ${edu.specialization}</p>` : ''}
                        ${edu.board ? `<p class="text-xs text-gray-600">Board/University: ${edu.board}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No education data available</p>');
        }
    }

    /**
     * Populate experience tab
     */
    function populateExperienceTab(data) {
        const $content = $('#modalExperienceContent');

        // Check for enhanced experience timeline data first
        if (data.experienceTimeline && data.experienceTimeline.length > 0) {
            let html = '<div class="space-y-4">';
            data.experienceTimeline.forEach(exp => {
                const borderColor = exp.isCurrent ? 'border-blue-400' : 'border-gray-300';
                html += `
                    <div class="border-l-4 ${borderColor} pl-4">
                        <div class="flex items-center gap-2">
                            <h5 class="font-semibold text-gray-900">${exp.title || 'Position'}</h5>
                            ${exp.isCurrent ? '<span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">Current</span>' : ''}
                        </div>
                        <p class="text-sm text-gray-600">${exp.institution || 'Organization'}</p>
                        <p class="text-xs text-gray-500">${exp.duration || 'Duration'}</p>
                        ${exp.description ? `<p class="text-sm text-gray-700 mt-2">${exp.description}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No experience data available</p>');
        }
    }

    /**
     * Populate skills tab
     */
    function populateSkillsTab(data) {
        const $skillsContent = $('#modalSkillsContent');
        const $certificationsContent = $('#modalCertificationsContent');

        // Skills
        if (data.skillsByCategory && Object.keys(data.skillsByCategory).length > 0) {
            let skillsHtml = '<div class="space-y-4">';
            Object.entries(data.skillsByCategory).forEach(([category, skills]) => {
                skillsHtml += `
                    <div>
                        <h5 class="font-semibold text-gray-900 mb-2 capitalize">${category.replace('_', ' ')}</h5>
                        <div class="flex flex-wrap gap-2">
                `;
                skills.forEach(skill => {
                    skillsHtml += `<span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">${skill.name || skill}</span>`;
                });
                skillsHtml += `</div></div>`;
            });
            skillsHtml += '</div>';
            $skillsContent.html(skillsHtml);
        } else if (data.special_skills) {
            const skills = data.special_skills.split(',').map(s => s.trim());
            let skillsHtml = '<div class="flex flex-wrap gap-2">';
            skills.forEach(skill => {
                skillsHtml += `<span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">${skill}</span>`;
            });
            skillsHtml += '</div>';
            $skillsContent.html(skillsHtml);
        } else {
            $skillsContent.html('<p class="text-sm text-gray-500">No skills data available</p>');
        }

        // Certifications
        if (data.certifications && data.certifications.length > 0) {
            let certHtml = '<div class="space-y-3">';
            data.certifications.forEach(cert => {
                certHtml += `
                    <div class="border-l-4 border-gray-200 pl-3">
                        <p class="font-semibold text-gray-900">${cert.name || 'Certification'}</p>
                        <p class="text-xs text-gray-600">${cert.issuer || 'Issuer'}</p>
                        <p class="text-xs text-gray-500">${cert.issueDate ? new Date(cert.issueDate).toLocaleDateString() : 'Date not specified'}</p>
                    </div>
                `;
            });
            certHtml += '</div>';
            $certificationsContent.html(certHtml);
        } else if (data.professional_certifications) {
            const certs = data.professional_certifications.split(',').map(c => c.trim());
            let certHtml = '<div class="space-y-2">';
            certs.forEach(cert => {
                certHtml += `<div class="text-sm"><p class="font-semibold text-gray-900">${cert}</p></div>`;
            });
            certHtml += '</div>';
            $certificationsContent.html(certHtml);
        } else {
            $certificationsContent.html('<p class="text-sm text-gray-500">No certifications available</p>');
        }
    }

    /**
     * Populate achievements tab
     */
    function populateAchievementsTab(data) {
        const $content = $('#modalAchievementsContent');

        if (data.achievementsByCategory && Object.keys(data.achievementsByCategory).length > 0) {
            let html = '<div class="space-y-6">';
            Object.entries(data.achievementsByCategory).forEach(([category, achievements]) => {
                html += `
                    <div>
                        <h5 class="font-semibold text-gray-900 mb-3 capitalize">${category.replace('_', ' ')}</h5>
                        <div class="space-y-3">
                `;
                achievements.forEach(achievement => {
                    html += `
                        <div class="border-l-4 border-gray-300 pl-4">
                            <h6 class="font-semibold text-gray-900">${achievement.title || 'Achievement'}</h6>
                            <p class="text-sm text-gray-600">${achievement.description || 'Description'}</p>
                            <p class="text-xs text-gray-500">${achievement.date ? new Date(achievement.date).toLocaleDateString() : 'Date not specified'}</p>
                        </div>
                    `;
                });
                html += `</div></div>`;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No achievements recorded</p>');
        }
    }

    /**
     * Show modal error
     */
    function showModalError(message) {
        $('#modalLoading').hide();
        $('.tab-content').addClass('hidden');
        $('#personalTab').removeClass('hidden').html(`
            <div class="p-6 text-center">
                <i class="fas fa-exclamation-triangle text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Data</h3>
                <p class="text-sm text-gray-500">${message}</p>
            </div>
        `);
    }

    /**
     * Generate teacher CV PDF using server-side enhanced PDF generation
     */
    function generateTeacherCV(teacherId, teacherName, $button) {
        const originalHtml = $button.html();
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...');

        // Use current teacher data if available, otherwise fetch it first
        if (currentTeacherData) {
            generatePDFFromData(currentTeacherData, $button, originalHtml);
        } else {
            // Fetch teacher data first, then generate PDF
            $.ajax({
                url: `/principal/api/teacher/profile-enhanced`,
                method: 'GET',
                data: { teacher_id: teacherId },
                success: function(response) {
                    if (response.success && response.teacher) {
                        currentTeacherData = response.teacher;
                        generatePDFFromData(response.teacher, $button, originalHtml);
                    } else {
                        console.error('Failed to load teacher data for PDF:', response.message);
                        $button.html('<i class="fas fa-times mr-2"></i>Error');
                        setTimeout(() => {
                            $button.html(originalHtml);
                        }, 2000);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading teacher data for PDF:', error);
                    $button.html('<i class="fas fa-times mr-2"></i>Error');
                    setTimeout(() => {
                        $button.html(originalHtml);
                    }, 2000);
                }
            });
        }
    }

    /**
     * Generate PDF from teacher data using server-side API
     */
    function generatePDFFromData(teacherData, $button, originalHtml) {
        console.log('🔄 Generating enhanced CV PDF for:', teacherData.name);

        // Call the enhanced PDF generation API
        $.ajax({
            url: '/principal/api/generate-enhanced-cv-pdf',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                teacherId: teacherData.id || teacherData.user_id,
                teacherData: teacherData
            }),
            success: function(response) {
                if (response.success && response.url) {
                    console.log('✅ PDF generated successfully:', response.url);

                    // Open PDF in new browser tab
                    const pdfUrl = response.url;
                    window.open(pdfUrl, '_blank');

                    $button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                    setTimeout(() => {
                        $button.html(originalHtml);
                    }, 2000);
                } else {
                    console.error('❌ PDF generation failed:', response.message);
                    $button.html('<i class="fas fa-times mr-2"></i>PDF Error');
                    setTimeout(() => {
                        $button.html(originalHtml);
                    }, 2000);
                }
            },
            error: function(xhr, status, error) {
                console.error('❌ Error generating PDF:', error);
                console.error('Response:', xhr.responseText);
                $button.html('<i class="fas fa-times mr-2"></i>PDF Error');
                setTimeout(() => {
                    $button.html(originalHtml);
                }, 2000);
            }
        });
    }

    /**
     * Open export modal
     */
    function openExportModal() {
        console.log('📊 Opening teacher export modal...');

        // Get currently selected teachers from the table
        const selectedTeachers = getSelectedTeachers();

        // Create and show export modal
        const modalHtml = `
            <div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <!-- Modal Header -->
                        <div class="flex items-center justify-between pb-4 border-b">
                            <h3 class="text-lg font-semibold text-gray-900">Export Teacher Data</h3>
                            <button id="closeExportModal" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>

                        <!-- Modal Body -->
                        <div class="py-4">
                            <!-- Export Options -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <!-- Format Selection -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Export Format</label>
                                    <div class="space-y-2">
                                        <label class="flex items-center">
                                            <input type="radio" name="exportFormat" value="excel" checked class="mr-2">
                                            <i class="fas fa-file-excel text-green-600 mr-2"></i>
                                            Excel (.xlsx)
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" name="exportFormat" value="pdf" class="mr-2">
                                            <i class="fas fa-file-pdf text-red-600 mr-2"></i>
                                            PDF (.pdf)
                                        </label>
                                    </div>
                                </div>

                                <!-- Export Information -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Export Information</label>
                                    <div class="p-4 bg-gray-50 rounded-lg">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                            <span class="text-sm font-medium text-gray-700">Export Details</span>
                                        </div>
                                        <div id="exportInfo" class="text-sm text-gray-600">
                                            ${selectedTeachers && selectedTeachers.length > 0 ?
                                                `Will export ${selectedTeachers.length} selected teachers` :
                                                'Will export all teachers'
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>



                            <!-- Column Selection -->
                            <div class="mb-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-md font-medium text-gray-700">
                                        <i class="fas fa-columns mr-2"></i>Select Columns to Export
                                    </h4>
                                    <div class="space-x-2">
                                        <button id="selectAllColumns" class="text-sm text-blue-600 hover:text-blue-800">Select All</button>
                                        <button id="deselectAllColumns" class="text-sm text-red-600 hover:text-red-800">Deselect All</button>
                                        <button id="selectBasicColumns" class="text-sm text-green-600 hover:text-green-800">Basic Info</button>
                                    </div>
                                </div>

                                <!-- Column Categories -->
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-64 overflow-y-auto border border-gray-200 rounded-lg p-4">
                                    <!-- Basic Information -->
                                    <div class="space-y-2">
                                        <h5 class="font-semibold text-gray-800 text-sm border-b pb-1">Basic Information</h5>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="id" class="mr-2 column-checkbox">ID</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="employee_id" class="mr-2 column-checkbox">Employee ID</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="name" class="mr-2 column-checkbox" checked>Name</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="email" class="mr-2 column-checkbox" checked>Email</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="date_of_birth" class="mr-2 column-checkbox">Date of Birth</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="gender" class="mr-2 column-checkbox">Gender</label>
                                    </div>

                                    <!-- Professional Information -->
                                    <div class="space-y-2">
                                        <h5 class="font-semibold text-gray-800 text-sm border-b pb-1">Professional</h5>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="designation" class="mr-2 column-checkbox" checked>Designation</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="department" class="mr-2 column-checkbox" checked>Department</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="joining_date" class="mr-2 column-checkbox">Joining Date</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="employment_type" class="mr-2 column-checkbox">Employment Type</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="subjects_taught" class="mr-2 column-checkbox">Subjects Taught</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="classes_handled" class="mr-2 column-checkbox">Classes Handled</label>
                                    </div>

                                    <!-- Contact Information -->
                                    <div class="space-y-2">
                                        <h5 class="font-semibold text-gray-800 text-sm border-b pb-1">Contact</h5>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="phone" class="mr-2 column-checkbox" checked>Phone</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="alternate_phone" class="mr-2 column-checkbox">Alternate Phone</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="emergency_contact" class="mr-2 column-checkbox">Emergency Contact</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="address" class="mr-2 column-checkbox">Address</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="city" class="mr-2 column-checkbox">City</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="state" class="mr-2 column-checkbox">State</label>
                                    </div>

                                    <!-- Experience & Performance -->
                                    <div class="space-y-2">
                                        <h5 class="font-semibold text-gray-800 text-sm border-b pb-1">Experience</h5>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="total_experience_years" class="mr-2 column-checkbox">Total Experience</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="teaching_experience_years" class="mr-2 column-checkbox">Teaching Experience</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="performance_rating" class="mr-2 column-checkbox">Performance Rating</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="current_salary" class="mr-2 column-checkbox">Current Salary</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="previous_organizations" class="mr-2 column-checkbox">Previous Organizations</label>
                                    </div>

                                    <!-- Education -->
                                    <div class="space-y-2">
                                        <h5 class="font-semibold text-gray-800 text-sm border-b pb-1">Education</h5>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="graduation_degree" class="mr-2 column-checkbox">Graduation Degree</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="graduation_university" class="mr-2 column-checkbox">Graduation University</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="post_graduation_degree" class="mr-2 column-checkbox">Post Graduation</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="phd_subject" class="mr-2 column-checkbox">PhD Subject</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="professional_certifications" class="mr-2 column-checkbox">Certifications</label>
                                    </div>

                                    <!-- Additional Information -->
                                    <div class="space-y-2">
                                        <h5 class="font-semibold text-gray-800 text-sm border-b pb-1">Additional</h5>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="special_skills" class="mr-2 column-checkbox">Special Skills</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="languages_known" class="mr-2 column-checkbox">Languages Known</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="awards_received" class="mr-2 column-checkbox">Awards Received</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="publications" class="mr-2 column-checkbox">Publications</label>
                                        <label class="flex items-center text-sm"><input type="checkbox" value="is_active" class="mr-2 column-checkbox">Active Status</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Selected Count Display -->
                            <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-blue-800">
                                        <i class="fas fa-info-circle mr-2"></i>
                                        <span id="selectedColumnsCount">5</span> columns selected for export
                                    </span>
                                    <span class="text-blue-600" id="estimatedRecords">
                                        ${selectedTeachers && selectedTeachers.length > 0 ?
                                            `${selectedTeachers.length} selected teachers will be exported` :
                                            'All teachers will be exported'
                                        }
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Modal Footer -->
                        <div class="flex items-center justify-between pt-4 border-t">
                            <button id="closeExportModalBtn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                                Cancel
                            </button>
                            <button id="startExportBtn" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                <i class="fas fa-download mr-2"></i>
                                Start Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        $('body').append(modalHtml);

        // Initialize export modal functionality
        initializeExportModal();
    }

    /**
     * Initialize export modal functionality
     */
    function initializeExportModal() {
        console.log('🔧 Initializing export modal...');

        // Close modal handlers
        $('#closeExportModal, #closeExportModalBtn').on('click', closeExportModal);

        // Column selection handlers
        $('#selectAllColumns').on('click', () => {
            $('.column-checkbox').prop('checked', true);
            updateSelectedColumnsCount();
        });

        $('#deselectAllColumns').on('click', () => {
            $('.column-checkbox').prop('checked', false);
            updateSelectedColumnsCount();
        });

        $('#selectBasicColumns').on('click', () => {
            $('.column-checkbox').prop('checked', false);
            $('input[value="name"], input[value="email"], input[value="designation"], input[value="department"], input[value="phone"]').prop('checked', true);
            updateSelectedColumnsCount();
        });

        // Update count when checkboxes change
        $('.column-checkbox').on('change', updateSelectedColumnsCount);

        // Start export handler
        $('#startExportBtn').on('click', startTeacherExport);

        // Initialize counts
        updateSelectedColumnsCount();
    }

    /**
     * Update selected columns count
     */
    function updateSelectedColumnsCount() {
        const count = $('.column-checkbox:checked').length;
        $('#selectedColumnsCount').text(count);
    }



    /**
     * Start teacher export process
     */
    function startTeacherExport() {
        console.log('🚀 Starting teacher export...');

        // Get selected columns
        const selectedColumns = [];
        $('.column-checkbox:checked').each(function() {
            selectedColumns.push($(this).val());
        });

        if (selectedColumns.length === 0) {
            ToastNotifications.error('Please select at least one column to export.');
            return;
        }

        // Get export parameters
        const format = $('input[name="exportFormat"]:checked').val();

        // Determine export type and teacher IDs based on current selection
        let type = 'all';
        let teacherIds = [];

        // Check if there are any teachers selected in the table
        const currentlySelectedTeachers = getSelectedTeachers();
        if (currentlySelectedTeachers.length > 0) {
            type = 'selected';
            teacherIds = currentlySelectedTeachers.map(t => t.id);
            console.log(`📋 Exporting ${teacherIds.length} selected teachers`);
        } else {
            console.log('📋 No teachers selected, exporting all teachers');
        }

        // Prepare export data
        const exportData = {
            format: format,
            type: type,
            columns: selectedColumns,
            filters: {}, // No filters needed with new logic
            teacherIds: teacherIds
        };

        console.log('Export data:', exportData);

        // Show loading notification
        const exportTypeText = type === 'selected' ? `${teacherIds.length} selected teachers` : 'all teachers';
        ToastNotifications.info(`Generating ${format.toUpperCase()} export for ${exportTypeText}...`);

        // Update button state
        const $button = $('#startExportBtn');
        const originalHtml = $button.html();
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating...').prop('disabled', true);

        // Make API call
        $.ajax({
            url: '/principal/api/export-teachers',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(exportData),
            success: function(response) {
                console.log('✅ Export successful:', response);

                if (response.success) {
                    // Close modal
                    closeExportModal();

                    // Show success notification
                    ToastNotifications.success(`Export generated successfully! ${response.recordCount} teachers exported.`);

                    // Open file in new tab
                    window.open(response.url, '_blank');
                } else {
                    console.error('❌ Export failed:', response.message);
                    ToastNotifications.error('Export failed: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('❌ Export error:', error);
                console.error('Response:', xhr.responseText);

                let errorMessage = 'Export error occurred';
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = errorResponse.message || errorMessage;
                } catch (e) {
                    errorMessage = error || errorMessage;
                }

                ToastNotifications.error(errorMessage);
            },
            complete: function() {
                $button.html(originalHtml).prop('disabled', false);
            }
        });
    }

    /**
     * Close export modal
     */
    function closeExportModal() {
        $('#exportModal').remove();
    }

    // Expose functions for debugging
    window.teacherDetails = {
        openModal,
        closeModal,
        generateTeacherCV,
        filterTeachers,
        openExportModal,
        closeExportModal
    };

    console.log('✅ Teacher Details System Ready');
});
